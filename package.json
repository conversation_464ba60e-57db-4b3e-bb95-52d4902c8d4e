{"name": "happy_fastapi_ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "bootstrap": "pnpm install", "type:check": "vue-tsc --noEmit", "lint:eslint": "eslint --cache --max-warnings 0  \"./**/*.{ts,tsx,vue}\" --fix", "lint:prettier": "prettier --ignore-unknown --check --cache --write \"./**/*.{vue,tsx,less,scss}\"", "lint:stylelint": "stylelint \"./**/*.{vue,less,scss,css}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:all": "npm run type:check && npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "lint": "pnpm run lint:all", "check": "pnpm run type:check && pnpm run lint", "generate:table": "node scripts/generate-table.js", "generate:api": "node scripts/generate-api.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@hookform/resolvers": "^5.0.1", "@internationalized/date": "^3.8.0", "@logicflow/core": "2.0.15", "@logicflow/extension": "^2.0.15", "@radix-icons/vue": "^1.0.0", "@stagewise-plugins/vue": "^0.6.1", "@stagewise/toolbar-vue": "^0.6.1", "@unovis/ts": "^1.5.0", "@unovis/vue": "^1.5.0", "@vee-validate/zod": "^4.15.0", "@visactor/vtable": "^1.18.0", "@visactor/vtable-editors": "^1.18.0", "@vueuse/core": "^12.8.2", "@vxe-ui/plugin-export-xlsx": "4.2.2", "add": "^2.0.6", "alova": "^3.2.10", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clipboard-polyfill": "^4.1.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "defu": "^6.1.4", "dotenv": "^16.4.7", "element-plus": "^2.9.11", "embla-carousel-vue": "^8.5.2", "exceljs": "^4.4.0", "gsap": "^3.13.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.507.0", "mathjs": "^14.4.0", "ngprogress": "^1.1.3", "nprogress": "^0.2.0", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "radix-vue": "^1.9.12", "reka-ui": "^2.3.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "type-fest": "^4.40.1", "v-calendar": "^3.1.2", "vaul-vue": "^0.2.0", "vee-validate": "^4.15.0", "viewerjs": "^1.11.7", "vue": "^3.5.13", "vue-file-upload": "^0.1.12", "vue-i18n": "11.0.0-rc.1", "vue-router": "4", "vue-sonner": "^1.3.0", "vue-types": "^6.0.0", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.7.19", "vxe-table": "^4.14.5", "vxe-table-plugin-export-xlsx": "4.0.6", "xe-utils": "^3.7.4", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.24.0", "@eslint/json": "^0.11.0", "@iconify/vue": "^4.3.0", "@logicflow/vue-node-registry": "1.0.17", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.7", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "prettier": "3.5.3", "sass-embedded": "^1.83.4", "tailwindcss": "^3.4.17", "terser": "^5.39.1", "text-encoding": "^0.7.0", "typescript": "~5.6.2", "typescript-eslint": "^8.29.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.7.0", "vue-tsc": "^2.2.0"}, "packageManager": "pnpm@10.10.0"}